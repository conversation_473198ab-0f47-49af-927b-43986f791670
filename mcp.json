{"mcpServers": {"context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"]}, "filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem@latest", "d:\\430\\business-components"]}, "sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking@latest"]}, "fetch": {"command": "npx", "args": ["-y", "fetch-mcp"]}, "playwright": {"command": "npx", "args": ["-y", "@executeautomation/playwright-mcp-server"]}, "memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"]}, "feedback-enhanced": {"command": "npx", "args": ["-y", "mcp-feedback-collector-md"]}}}