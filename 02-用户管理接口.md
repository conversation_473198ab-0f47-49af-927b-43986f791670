# 用户管理接口文档

## 一、用户管理接口

### 1.1 分页查询用户

**接口地址**：`/api/user/page`

**请求方式**：`POST`

**接口说明**：分页查询用户列表

**请求参数**：

| 参数名   | 参数类型 | 必填 | 描述                 |
| -------- | -------- | ---- | -------------------- |
| username | String   | 否   | 用户名（模糊查询）   |
| realName | String   | 否   | 真实姓名（模糊查询） |
| phone    | String   | 否   | 手机号（模糊查询）   |
| email    | String   | 否   | 邮箱（模糊查询）     |
| status   | Integer  | 否   | 状态（0-禁用，1-正常，2-锁定） |
| pageNum  | Integer  | 是   | 页码                 |
| pageSize | Integer  | 是   | 每页条数             |

**请求示例**：

```json
{
    "username": "",
    "realName": "",
    "phone": "",
    "email": "",
    "status": 1,
    "pageNum": 1,
    "pageSize": 10
}
```

**curl命令**：
```bash
curl -X POST "http://127.0.0.1:8413/template/api/user/page" \
  -H "Content-Type: application/json" \
  -H "Authorization: {{kl-token}}" \
  -d '{
    "username": "",
    "realName": "",
    "phone": "",
    "email": "",
    "status": 1,
    "pageNum": 1,
    "pageSize": 10
}'
```

**响应示例**：

```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "total": 10,
        "list": [
            {
                "id": 1,
                "username": "admin",
                "realName": "管理员",
                "email": "<EMAIL>",
                "phone": "13800138000",
                "avatar": "avatar.jpg",
                "gender": 1,
                "status": 1,
                "statusLabel": "正常",
                "lastLoginTime": "2023-05-10T10:12:30",
                "createTime": "2023-05-10T10:12:30",
                "updateTime": "2023-05-10T10:12:30"
            }
        ],
        "pageNum": 1,
        "pageSize": 10,
        "pages": 1
    }
}
```

### 1.2 创建用户

**接口地址**：`/api/user/create`

**请求方式**：`POST`

**接口说明**：创建用户

**请求参数**：

| 参数名       | 参数类型 | 必填 | 描述                               |
| ------------ | -------- | ---- | ---------------------------------- |
| username     | String   | 是   | 用户名（唯一）                     |
| password     | String   | 是   | 密码                               |
| realName     | String   | 否   | 真实姓名                           |
| email        | String   | 否   | 邮箱                               |
| phone        | String   | 否   | 手机号                             |
| avatar       | String   | 否   | 头像URL                            |
| gender       | Integer  | 否   | 性别：0-未知，1-男，2-女           |
| status       | Integer  | 否   | 状态：0-禁用，1-正常，2-锁定       |
| departmentId | Long     | 否   | 部门ID                             |
| roleIds      | List<Long> | 否 | 角色ID列表                         |

**请求示例**：

```json
{
    "username": "zhangsan",
    "password": "123456",
    "realName": "张三",
    "email": "<EMAIL>",
    "phone": "13800138001",
    "avatar": "avatar.jpg",
    "gender": 1,
    "status": 1,
    "departmentId": 1
}
```

**curl命令**：
```bash
curl -X POST "http://127.0.0.1:8413/template/api/user/create" \
  -H "Content-Type: application/json" \
  -H "Authorization: {{kl-token}}" \
  -d '{
    "username": "zhangsan",
    "password": "123456",
    "realName": "张三",
    "email": "<EMAIL>",
    "phone": "13800138001",
    "avatar": "avatar.jpg",
    "gender": 1,
    "status": 1,
    "departmentId": 1
}'
```

**响应示例**：

```json
{
    "code": 200,
    "message": "操作成功",
    "data": 1
}
```

### 1.3 更新用户

**接口地址**：`/api/user/update`

**请求方式**：`PUT`

**接口说明**：更新用户信息

**请求参数**：

| 参数名       | 参数类型 | 必填 | 描述                               |
| ------------ | -------- | ---- | ---------------------------------- |
| id           | Long     | 是   | 用户ID                             |
| realName     | String   | 否   | 真实姓名                           |
| email        | String   | 否   | 邮箱                               |
| phone        | String   | 否   | 手机号                             |
| avatar       | String   | 否   | 头像URL                            |
| gender       | Integer  | 否   | 性别：0-未知，1-男，2-女           |
| status       | Integer  | 否   | 状态：0-禁用，1-正常，2-锁定       |
| departmentId | Long     | 否   | 部门ID                             |

**请求示例**：

```json
{
    "id": 1,
    "realName": "张三-更新",
    "email": "<EMAIL>",
    "phone": "13800138002",
    "avatar": "avatar_new.jpg",
    "gender": 1,
    "status": 1,
    "departmentId": 2
}
```

**curl命令**：
```bash
curl -X PUT "http://127.0.0.1:8413/template/api/user/update" \
  -H "Content-Type: application/json" \
  -H "Authorization: {{kl-token}}" \
  -d '{
    "id": 1,
    "realName": "张三-更新",
    "email": "<EMAIL>",
    "phone": "13800138002",
    "avatar": "avatar_new.jpg",
    "gender": 1,
    "status": 1,
    "departmentId": 2
}'
```

**响应示例**：

```json
{
    "code": 200,
    "message": "操作成功",
    "data": true
}
```

### 1.4 删除用户

**接口地址**：`/api/user/delete`

**请求方式**：`DELETE`

**接口说明**：删除用户

**请求参数**：

| 参数名 | 参数类型 | 必填 | 描述   |
| ------ | -------- | ---- | ------ |
| id     | Long     | 是   | 用户ID |

**curl命令**：
```bash
curl -X DELETE "http://127.0.0.1:8413/template/api/user/delete?id=1" \
  -H "Content-Type: application/json" \
  -H "Authorization: {{kl-token}}"
```

**响应示例**：

```json
{
    "code": 200,
    "message": "操作成功",
    "data": true
}
```

### 1.5 获取用户详情

**接口地址**：`/api/user/getById`

**请求方式**：`GET`

**接口说明**：根据ID获取用户详情

**请求参数**：

| 参数名 | 参数类型 | 必填 | 描述   |
| ------ | -------- | ---- | ------ |
| id     | Long     | 是   | 用户ID |

**curl命令**：
```bash
curl -X GET "http://127.0.0.1:8413/template/api/user/getById?id=1" \
  -H "Content-Type: application/json" \
  -H "Authorization: {{kl-token}}"
```

**响应示例**：

```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "id": 1,
        "username": "admin",
        "realName": "管理员",
        "email": "<EMAIL>",
        "phone": "13800138000",
        "avatar": "avatar.jpg",
        "gender": 1,
        "status": 1,
        "statusLabel": "正常",
        "lastLoginTime": "2023-05-10T10:12:30",
        "createTime": "2023-05-10T10:12:30",
        "updateTime": "2023-05-10T10:12:30"
    }
}
```

### 1.6 修改用户密码

**接口地址**：`/api/user/changePassword`

**请求方式**：`POST`

**接口说明**：修改用户密码

**请求参数**：

| 参数名      | 参数类型 | 必填 | 描述     |
| ----------- | -------- | ---- | -------- |
| userId      | Long     | 是   | 用户ID   |
| oldPassword | String   | 是   | 旧密码   |
| newPassword | String   | 是   | 新密码   |

**curl命令**：
```bash
curl -X POST "http://127.0.0.1:8413/template/api/user/changePassword" \
  -H "Content-Type: application/json" \
  -H "Authorization: {{kl-token}}" \
  -d '{
    "userId": 1,
    "oldPassword": "123456",
    "newPassword": "newpassword"
}'
```

**响应示例**：

```json
{
    "code": 200,
    "message": "操作成功",
    "data": true
}
```

### 1.7 重置用户密码

**接口地址**：`/api/user/resetPassword`

**请求方式**：`POST`

**接口说明**：重置用户密码

**请求参数**：

| 参数名      | 参数类型 | 必填 | 描述     |
| ----------- | -------- | ---- | -------- |
| userId      | Long     | 是   | 用户ID   |
| newPassword | String   | 是   | 新密码   |

**curl命令**：
```bash
curl -X POST "http://127.0.0.1:8413/template/api/user/resetPassword" \
  -H "Content-Type: application/json" \
  -H "Authorization: {{kl-token}}" \
  -d '{
    "userId": 1,
    "newPassword": "123456"
}'
```

**响应示例**：

```json
{
    "code": 200,
    "message": "操作成功",
    "data": true
}
```

### 1.8 更新用户状态

**接口地址**：`/api/user/updateStatus`

**请求方式**：`POST`

**接口说明**：更新用户状态

**请求参数**：

| 参数名 | 参数类型 | 必填 | 描述                           |
| ------ | -------- | ---- | ------------------------------ |
| userId | Long     | 是   | 用户ID                         |
| status | Integer  | 是   | 状态：0-禁用，1-正常，2-锁定   |

**curl命令**：
```bash
curl -X POST "http://127.0.0.1:8413/template/api/user/updateStatus" \
  -H "Content-Type: application/json" \
  -H "Authorization: {{kl-token}}" \
  -d '{
    "userId": 1,
    "status": 1
}'
```

**响应示例**：

```json
{
    "code": 200,
    "message": "操作成功",
    "data": true
}
```
